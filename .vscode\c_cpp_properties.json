{
    "configurations": [
        {
            "name": "Win32",
            "includePath": [
                "${workspaceFolder}/**",

                // These are standard paths for Arduino CLI core and libraries
                "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/hardware/avr/1.8.6/**",
                "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/hardware/avr/1.8.6/cores/arduino",
                "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/hardware/avr/1.8.6/variants/mega",
                "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/hardware/avr/1.8.6/libraries/**",
                "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/tools/avr-gcc/7.3.0-atmel3.6.1-arduino7/avr/include/avr/**",
                "C:/Users/<USER>/Documents/Arduino/libraries/**"
            ],
            "defines": [
                "ARDUINO=10819",
                "ARDUINO_AVR_MEGA2560",
                "ARDUINO_ARCH_AVR",
                "F_CPU=16000000L",
                "__AVR_ATmega2560__",
                "_DEBUG"
            ],
            "compilerPath": "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/tools/avr-gcc/7.3.0-atmel3.6.1-arduino7/bin/avr-gcc.exe",
            "cStandard": "c11",
            "cppStandard": "c++11",
            "intelliSenseMode": "gcc-x64",
            "forcedInclude": [
                "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/hardware/avr/1.8.6/cores/arduino/Arduino.h",
                "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/tools/avr-gcc/7.3.0-atmel3.6.1-arduino7/avr/include/avr/wdt.h"                
            ]
        }
    ],
    "version": 4
}