{
    // Path to Arduino CLI executable (from Arduino IDE installation)
    "arduino.path": "C:/Program Files/Arduino IDE/resources/app/lib/backend/resources",
    "arduino.commandPath": "arduino-cli.exe",
    "arduino.useArduinoCli": true,

    "arduino.logLevel": "info",
    "arduino.enableUSBDetection": true,
    "arduino.disableTestingOpen": false,
    "arduino.skipHeaderProvider": false,
    "arduino.additionalUrls": [],
    "arduino.defaultBaudRate": 9600,
    "C_Cpp.intelliSenseEngine": "default",
    "files.associations": {
        "*.ino": "cpp"
    }
}