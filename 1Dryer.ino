#include <EEPROM.h>
#include <PID_v1.h> // https://github.com/br3ndonland/PID-v1
#include <avr/wdt.h>
#include <avr/pgmspace.h>
#include <string.h>
#include <stdio.h>

// ──────────────────────────────────────────────────────────────────────────────
// PIN ASSIGNMENTS
// ──────────────────────────────────────────────────────────────────────────────
static constexpr uint8_t THERM_PIN       = A0;
static constexpr uint8_t SSR_PIN         = 3;
static constexpr uint8_t LED_PIN         = 13;
static constexpr uint8_t BUTTON_ON_PIN   = 4;
static constexpr uint8_t BUTTON_UP_PIN   = 5;
static constexpr uint8_t BUTTON_DOWN_PIN = 6;
static constexpr uint8_t BUTTON_CAL_PIN  = 7;

// ──────────────────────────────────────────────────────────────────────────────
// SYSTEM CONSTANTS
// ──────────────────────────────────────────────────────────────────────────────
static constexpr float MIN_TEMP_FAIL   = 10.0f;
static constexpr float MAX_TEMP_FAIL   = 90.0f;
static constexpr float DEFAULT_SETPT   = 80.0f;
static constexpr float STEP_TEMP       = 1.0f;
static constexpr float MIN_SETPT       = 15.0f;
static constexpr float MAX_SETPT       = 80.0f;

static constexpr unsigned long CONTROL_WINDOW = 1000UL;
static constexpr unsigned long DEBOUNCE_MS    = 50UL;
static constexpr unsigned long LONG_PRESS_MS  = 2000UL;

// ──────────────────────────────────────────────────────────────────────────────
// EEPROM ADDRESSES
// ──────────────────────────────────────────────────────────────────────────────
static constexpr uint16_t ADDR_FLAG = 0;
static constexpr uint16_t ADDR_KP   = ADDR_FLAG + sizeof(uint8_t);
static constexpr uint16_t ADDR_KI   = ADDR_KP   + sizeof(float);
static constexpr uint16_t ADDR_KD   = ADDR_KI   + sizeof(float);

// ──────────────────────────────────────────────────────────────────────────────
// TEMPERATURE TABLE FOR NTC100K (5V ADC, 10-bit)
// ──────────────────────────────────────────────────────────────────────────────
static constexpr uint8_t T_STEPS = 11;
static const uint16_t ADC_TAB[T_STEPS]   PROGMEM = {930,885,820,743,659,569,479,393,316,251,108};
static const uint16_t TEMP_TAB[T_STEPS] PROGMEM = {  0, 10, 20, 30, 40, 50, 60, 70, 80, 90,100};

// ──────────────────────────────────────────────────────────────────────────────
// STATE MACHINE
// ──────────────────────────────────────────────────────────────────────────────
enum SystemState { STATE_OFF, STATE_ON, STATE_FAULT };
static SystemState sysState;

// ──────────────────────────────────────────────────────────────────────────────
// PID OBJECT & I/O
// ──────────────────────────────────────────────────────────────────────────────
static double Setpoint, Input, Output;
static double Kp, Ki, Kd;
PID pid(&Input, &Output, &Setpoint, 0.0, 0.0, 0.0, DIRECT);

// ──────────────────────────────────────────────────────────────────────────────
// TEMP BUFFER
// ──────────────────────────────────────────────────────────────────────────────
static constexpr uint8_t TEMP_SAMPLES = 10;
uint16_t tempBuf[TEMP_SAMPLES];
uint8_t  tempIdx    = 0;
long     tempSum    = 0;
bool     tempBufInit= false;

// ──────────────────────────────────────────────────────────────────────────────
// BUTTON STRUCT
// ──────────────────────────────────────────────────────────────────────────────
struct Button {
  uint8_t pin;
  bool    stableState;
  bool    lastReading;
  unsigned long lastDebounceTime;
  unsigned long pressStartTime;
  bool    longPressEvent;
} onBtn, upBtn, downBtn, calBtn;

// ──────────────────────────────────────────────────────────────────────────────
// FORWARD DECLARATIONS
// ──────────────────────────────────────────────────────────────────────────────
void initButton(Button &b, uint8_t pin);
int  updateButton(Button &b);
void handleButtons();
void updateTempBuffer();
float readTemperature();
void loadPID();
void savePID();
void autoTunePID(float targetTemp, uint8_t cycles);

// ──────────────────────────────────────────────────────────────────────────────
// GLOBAL TIMING
// ──────────────────────────────────────────────────────────────────────────────
unsigned long windowStart;
unsigned long lastStatus;

// ──────────────────────────────────────────────────────────────────────────────
// SETUP
// ──────────────────────────────────────────────────────────────────────────────
void setup() {
  wdt_disable();
  Serial.begin(9600);

  pinMode(SSR_PIN, OUTPUT);
  digitalWrite(SSR_PIN, LOW);
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, LOW);

  initButton(onBtn,   BUTTON_ON_PIN);
  initButton(upBtn,   BUTTON_UP_PIN);
  initButton(downBtn, BUTTON_DOWN_PIN);
  initButton(calBtn,  BUTTON_CAL_PIN);

  // Initialize temperature buffer
  for (uint8_t i = 0; i < TEMP_SAMPLES; i++) {
    tempBuf[i] = analogRead(THERM_PIN);
    tempSum   += tempBuf[i];
  }
  tempBufInit = true;

  loadPID();
  pid.SetOutputLimits(0, CONTROL_WINDOW);
  pid.SetSampleTime(100);
  pid.SetMode(AUTOMATIC);

  Setpoint = DEFAULT_SETPT;
  sysState = STATE_OFF;

  // Initialize timing
  windowStart = millis();
  lastStatus  = millis();

  Serial.println(F("=== Heater Controller Ready ==="));
  Serial.println(F("Press ON/OFF to start; long-press CAL for PID autotune."));
}

// ──────────────────────────────────────────────────────────────────────────────
// MAIN LOOP
// ──────────────────────────────────────────────────────────────────────────────
void loop() {
  updateTempBuffer();
  handleButtons();

  unsigned long now = millis();

  switch (sysState) {

    case STATE_OFF:
      digitalWrite(SSR_PIN, LOW);
      digitalWrite(LED_PIN, LOW);
      break;

    case STATE_ON: {
      digitalWrite(LED_PIN, HIGH);
      Input = readTemperature();
      if (Input < MIN_TEMP_FAIL || Input > MAX_TEMP_FAIL) {
        sysState = STATE_FAULT;
        Serial.println(F("ERROR: Thermistor failure detected, shutting down"));
        break;
      }

      pid.Compute();
      while (now - windowStart >= CONTROL_WINDOW)
        windowStart += CONTROL_WINDOW;

      unsigned long onTime = (unsigned long)Output;
      if (now - windowStart < onTime)
        digitalWrite(SSR_PIN, HIGH);
      else
        digitalWrite(SSR_PIN, LOW);

      if (now - lastStatus >= 1000) {
        lastStatus = now;
        Serial.print(F("T=")); Serial.print(Input,1);
        Serial.print(F("C SP=")); Serial.print(Setpoint,1);
        Serial.print(F("C OUT=")); Serial.print((onTime*100UL)/CONTROL_WINDOW);
        Serial.println(F("%"));
      }
      break;
    }

    case STATE_FAULT:
      digitalWrite(SSR_PIN, LOW);
      digitalWrite(LED_PIN, (now/200)%2 ? HIGH : LOW);
      break;
  }
}

// ──────────────────────────────────────────────────────────────────────────────
// BUTTON HELPERS
// ──────────────────────────────────────────────────────────────────────────────
void initButton(Button &b, uint8_t pin) {
  b.pin             = pin;
  pinMode(pin, INPUT_PULLUP);
  b.lastReading     = digitalRead(pin);
  b.stableState     = b.lastReading;
  b.lastDebounceTime= millis();
  b.longPressEvent  = false;
}

int updateButton(Button &b) {
  bool reading = digitalRead(b.pin);
  if (reading != b.lastReading) {
    b.lastDebounceTime = millis();
    b.lastReading      = reading;
  }
  int event = 0;
  if (millis() - b.lastDebounceTime > DEBOUNCE_MS) {
    if (reading != b.stableState) {
      b.stableState = reading;
      if (!reading) { // pressed
        b.pressStartTime = millis();
        b.longPressEvent = false;
      } else { // released
        unsigned long held = millis() - b.pressStartTime;
        if (held < LONG_PRESS_MS) event = 1;
      }
    }
  }
  if (!b.stableState && !b.longPressEvent && (millis()-b.pressStartTime >= LONG_PRESS_MS)) {
    b.longPressEvent = true;
    event = 2;
  }
  return event;
}

void handleButtons() {
  int onEv   = updateButton(onBtn);
  int upEv   = updateButton(upBtn);
  int downEv = updateButton(downBtn);
  int calEv  = updateButton(calBtn);

  if (onEv == 1) {
    if (sysState == STATE_OFF) {
      sysState = STATE_ON;
      Serial.println(F("=== System ON ==="));
    } else if (sysState == STATE_ON) {
      sysState = STATE_OFF;
      Serial.println(F("=== System OFF ==="));
    } else {
      sysState = STATE_OFF;
      Serial.println(F("=== Fault Cleared, System OFF ==="));
    }
  }
  if (upEv == 1 && sysState == STATE_OFF) {
    Setpoint = min(Setpoint + STEP_TEMP, MAX_SETPT);
    Serial.print(F("[INFO] Setpoint = ")); Serial.print(Setpoint,1); Serial.println(F(" C"));
  }
  if (downEv == 1 && sysState == STATE_OFF) {
    Setpoint = max(Setpoint - STEP_TEMP, MIN_SETPT);
    Serial.print(F("[INFO] Setpoint = ")); Serial.print(Setpoint,1); Serial.println(F(" C"));
  }
  if (calEv == 2 && sysState == STATE_OFF) {
    Serial.println(F("Starting PID Autotune..."));
    autoTunePID(DEFAULT_SETPT, 6);
  }
}

// ──────────────────────────────────────────────────────────────────────────────
// TEMP BUFFER UPDATE
// ──────────────────────────────────────────────────────────────────────────────
void updateTempBuffer() {
  if (!tempBufInit) return;
  int val = analogRead(THERM_PIN);
  tempSum    -= tempBuf[tempIdx];
  tempSum    += val;
  tempBuf[tempIdx] = val;
  tempIdx    = (tempIdx + 1) % TEMP_SAMPLES;
}

// ──────────────────────────────────────────────────────────────────────────────
// READ NTC TEMPERATURE (TABLE INTERPOLATION)
// ──────────────────────────────────────────────────────────────────────────────
float readTemperature() {
  int adc = tempSum / TEMP_SAMPLES;
  uint16_t first = pgm_read_word(&ADC_TAB[0]);
  uint16_t last  = pgm_read_word(&ADC_TAB[T_STEPS-1]);
  for (uint8_t i = 0; i < T_STEPS-1; i++) {
    uint16_t a = pgm_read_word(&ADC_TAB[i]);
    uint16_t b = pgm_read_word(&ADC_TAB[i+1]);
    if (adc <= a && adc >= b) {
      float t1 = pgm_read_word(&TEMP_TAB[i]);
      float t2 = pgm_read_word(&TEMP_TAB[i+1]);
      return t1 + (a - adc) * (t2 - t1) / float(a - b);
    }
  }
  if (adc > first) return pgm_read_word(&TEMP_TAB[0]);
  if (adc < last)  return pgm_read_word(&TEMP_TAB[T_STEPS-1]);
  Serial.println(F("ERROR: ADC interp failure"));
  return -999;
}

// ──────────────────────────────────────────────────────────────────────────────
// PID EEPROM LOAD & SAVE
// ──────────────────────────────────────────────────────────────────────────────
void loadPID() {
  if (EEPROM.read(ADDR_FLAG) == 0xAB) {
    EEPROM.get(ADDR_KP, Kp);
    EEPROM.get(ADDR_KI, Ki);
    EEPROM.get(ADDR_KD, Kd);
    if (Kp <= 0 || Ki < 0 || Kd < 0) {
      Kp = 2.0; Ki = 5.0; Kd = 1.0;
      Serial.println(F("Invalid PID in EEPROM; using defaults."));
    }
  } else {
    Kp = 2.0; Ki = 5.0; Kd = 1.0;
    Serial.println(F("No PID in EEPROM; using defaults."));
  }
  pid.SetTunings(Kp, Ki, Kd);
}

void savePID() {
  EEPROM.put(ADDR_KP, Kp);
  EEPROM.put(ADDR_KI, Ki);
  EEPROM.put(ADDR_KD, Kd);
  EEPROM.update(ADDR_FLAG, 0xAB);
}

// ──────────────────────────────────────────────────────────────────────────────
// PID AUTOTUNE (Relay Method)
// ──────────────────────────────────────────────────────────────────────────────
void autoTunePID(float targetTemp, uint8_t cycles) {
  static constexpr float AUTOTUNE_PI = 3.14159265358979323846f;
  Serial.print(F("PID Autotune: target "));
  Serial.println(targetTemp, 1);

  float maxTemp = -1e6f;
  float minTemp =  1e6f;
  unsigned long lastCross = 0;
  float periods[10] = {0};
  uint8_t count = 0;
  bool heating = true;

  digitalWrite(SSR_PIN, HIGH);
  unsigned long start = millis();
  while (count < cycles) {
    updateTempBuffer();
    float temp = readTemperature();
    unsigned long now = millis();

    if (temp > maxTemp) maxTemp = temp;
    if (temp < minTemp) minTemp = temp;

    if (heating && temp >= targetTemp) {
      digitalWrite(SSR_PIN, LOW);
      if (lastCross) periods[count++] = (now - lastCross) / 1000.0f;
      lastCross = now;
      heating = false;
    } else if (!heating && temp <= targetTemp) {
      digitalWrite(SSR_PIN, HIGH);
      lastCross = now;
      heating = true;
    }

    if (now - start > (unsigned long)cycles * CONTROL_WINDOW * 10) {
      Serial.println(F("Autotune timeout"));
      
      digitalWrite(SSR_PIN, LOW);
      return;
    }

    delay(10);
  }

  float Pu = 0;
  for (uint8_t i = 0; i < count; i++) Pu += periods[i];
  Pu /= count;
  float a = (maxTemp - minTemp) / 2.0f;
  if (a < 1e-3f) {
    Serial.println(F("Autotune failed: too small amplitude"));
    digitalWrite(SSR_PIN, LOW);
    return;
  }

  float Ku = 4.0f / (AUTOTUNE_PI * a);
  double newKp = 0.6 * Ku;
  double newKi = 1.2 * Ku / Pu;
  double newKd = 0.075 * Ku * Pu;

  Kp = newKp; Ki = newKi; Kd = newKd;
  pid.SetTunings(Kp, Ki, Kd);
  savePID();

  digitalWrite(SSR_PIN, LOW);
  Serial.println(F("Autotune complete:"));
  Serial.print(F(" Kp=")); Serial.println(Kp);
  Serial.print(F(" Ki=")); Serial.println(Ki);
  Serial.print(F(" Kd=")); Serial.println(Kd);
}





