# 1Dryer - Arduino Heater Controller

This is an Arduino-based heater controller with PID temperature control, designed for Arduino Mega 2560.

## Features

- PID temperature control with auto-tuning capability
- NTC thermistor temperature sensing with lookup table interpolation
- <PERSON>ton interface for system control and setpoint adjustment
- EEPROM storage for PID parameters
- Safety features including temperature fault detection
- Serial output for monitoring and debugging

## Hardware Requirements

- Arduino Mega 2560
- NTC 100K thermistor
- Solid State Relay (SSR)
- Push buttons (4x)
- LED indicator

## Pin Assignments

- A0: Thermistor input
- Pin 3: SSR control output
- Pin 4: ON/OFF button
- Pin 5: UP button
- Pin 6: DOWN button
- Pin 7: CALIBRATION button
- Pin 13: Status LED

## Dependencies

- PID_v1 library (already installed in Arduino libraries folder)
- Arduino core libraries (EEPROM, avr/wdt, avr/pgmspace)

## Building the Project

### Option 1: Using the Build Script (Recommended)

Run the PowerShell build script to compile the project:

```powershell
powershell -ExecutionPolicy Bypass -File build.ps1
```

This script will:
- Check for required Arduino toolchain
- Compile the sketch using AVR-GCC
- Generate object files in the `build/` directory

### Option 2: Using Arduino IDE

1. Open `1Dryer.ino` in Arduino IDE
2. Select "Arduino Mega 2560" as the board
3. Install the PID_v1 library if not already installed
4. Compile and upload to your Arduino

## Recent Fixes Applied

### 1. VSCode Configuration Issues Fixed

**Problem**: The `.vscode/c_cpp_properties.json` file had incorrect compiler settings mixing MSVC and Arduino AVR toolchain.

**Solution**: Updated the configuration to use proper AVR-GCC compiler paths and settings:
- Changed `compilerPath` from `cl.exe` to AVR-GCC path
- Updated `intelliSenseMode` from `msvc-x64` to `gcc-x64`
- Added proper Arduino-specific defines
- Fixed indentation issues

### 2. C++ Compatibility Issues Fixed

**Problem**: The code used `constexpr` which may not be supported in older Arduino cores.

**Solution**: Replaced all `constexpr` declarations with `const` for better compatibility:
- Pin assignments (THERM_PIN, SSR_PIN, etc.)
- System constants (temperature limits, timing values)
- EEPROM addresses
- Array sizes and mathematical constants

### 3. Build System Created

**Problem**: No easy way to compile and test the code outside of Arduino IDE.

**Solution**: Created `build.ps1` PowerShell script that:
- Uses the existing Arduino toolchain
- Properly includes Arduino.h header
- Sets up correct include paths for Arduino core and libraries
- Provides clear compilation feedback

## Usage

1. **Power On**: Press the ON/OFF button to start the system
2. **Set Temperature**: Use UP/DOWN buttons when system is OFF to adjust setpoint
3. **Auto-tune PID**: Long-press the CAL button when system is OFF to run PID auto-tuning
4. **Monitor**: Check serial output at 9600 baud for temperature readings and system status

## Safety Features

- Automatic shutdown if thermistor readings are out of range (10°C - 90°C)
- Fault state indication with blinking LED
- Watchdog timer support
- Temperature limits enforced in software

## Serial Commands

The system outputs status information via serial at 9600 baud:
- Temperature readings
- Setpoint values
- PID output percentage
- System state changes
- Error messages

## Troubleshooting

If you encounter compilation issues:

1. Ensure Arduino toolchain is installed in the expected location
2. Verify PID_v1 library is installed in Arduino libraries folder
3. Check that all required include paths exist
4. Run the build script for detailed error messages

## License

This project is provided as-is for educational and hobbyist use.
