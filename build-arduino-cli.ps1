# Arduino CLI Build Script for 1Dryer.ino
# This script compiles the Arduino sketch using Arduino CLI

$ErrorActionPreference = "Stop"

# Define Arduino CLI path
$ARDUINO_CLI = "C:\Program Files\Arduino IDE\resources\app\lib\backend\resources\arduino-cli.exe"

# Check if Arduino CLI exists
if (-not (Test-Path $ARDUINO_CLI)) {
    Write-Error "Arduino CLI not found at: $ARDUINO_CLI"
    Write-Host "Please install Arduino IDE or update the path in this script."
    exit 1
}

Write-Host "=== Arduino CLI Build Script ===" -ForegroundColor Cyan
Write-Host "Project: 1Dryer.ino" -ForegroundColor White
Write-Host "Target: Arduino Nano (ATmega328P)" -ForegroundColor White
Write-Host ""

# Check Arduino CLI version
Write-Host "Checking Arduino CLI version..." -ForegroundColor Yellow
try {
    $version = & $ARDUINO_CLI version
    Write-Host "✅ $version" -ForegroundColor Green
} catch {
    Write-Error "Failed to get Arduino CLI version: $($_.Exception.Message)"
    exit 1
}

# Check if required core is installed
Write-Host "Checking Arduino AVR core..." -ForegroundColor Yellow
$coreList = & $ARDUINO_CLI core list
if ($coreList -match "arduino:avr") {
    Write-Host "✅ Arduino AVR core is installed" -ForegroundColor Green
} else {
    Write-Host "❌ Arduino AVR core not found. Installing..." -ForegroundColor Red
    & $ARDUINO_CLI core install arduino:avr
}

# Check if PID library is available
Write-Host "Checking PID library..." -ForegroundColor Yellow
$libList = & $ARDUINO_CLI lib list
if ($libList -match "PID") {
    Write-Host "✅ PID library is available" -ForegroundColor Green
} else {
    Write-Host "❌ PID library not found. Installing..." -ForegroundColor Red
    & $ARDUINO_CLI lib install PID
}

# Compile the sketch
Write-Host "Compiling 1Dryer.ino..." -ForegroundColor Yellow
Write-Host "Board: Arduino Nano ATmega328P (arduino:avr:nano:cpu=atmega328)" -ForegroundColor White

try {
    $compileOutput = & $ARDUINO_CLI compile --fqbn arduino:avr:nano:cpu=atmega328 --verbose 1Dryer.ino 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Compilation successful!" -ForegroundColor Green
        
        # Extract memory usage information
        $memoryInfo = $compileOutput | Where-Object { $_ -match "Sketch uses|Global variables use" }
        if ($memoryInfo) {
            Write-Host ""
            Write-Host "Memory Usage:" -ForegroundColor Cyan
            $memoryInfo | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
        }
        
        # Extract library information
        $libraryInfo = $compileOutput | Where-Object { $_ -match "Using library" }
        if ($libraryInfo) {
            Write-Host ""
            Write-Host "Libraries Used:" -ForegroundColor Cyan
            $libraryInfo | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
        }
        
        # Show output files location
        $buildPath = $compileOutput | Where-Object { $_ -match "sketches\\" } | Select-Object -First 1
        if ($buildPath -match "sketches\\([A-F0-9]+)") {
            $sketchHash = $matches[1]
            $outputPath = "C:\Users\<USER>\AppData\Local\arduino\sketches\$sketchHash"
            Write-Host ""
            Write-Host "Output Files:" -ForegroundColor Cyan
            Write-Host "  Location: $outputPath" -ForegroundColor White
            
            if (Test-Path $outputPath) {
                $hexFile = Get-ChildItem "$outputPath\*.hex" | Select-Object -First 1
                $elfFile = Get-ChildItem "$outputPath\*.elf" | Select-Object -First 1
                
                if ($hexFile) {
                    Write-Host "  HEX file: $($hexFile.Name) ($([math]::Round($hexFile.Length/1KB, 2)) KB)" -ForegroundColor White
                }
                if ($elfFile) {
                    Write-Host "  ELF file: $($elfFile.Name) ($([math]::Round($elfFile.Length/1KB, 2)) KB)" -ForegroundColor White
                }
            }
        }
        
    } else {
        Write-Host "❌ Compilation failed!" -ForegroundColor Red
        Write-Host "Error output:" -ForegroundColor Red
        $compileOutput | Where-Object { $_ -match "error:" } | ForEach-Object { 
            Write-Host "  $_" -ForegroundColor Red 
        }
        exit 1
    }
    
} catch {
    Write-Host "❌ Compilation error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== Build Complete ===" -ForegroundColor Cyan
Write-Host "Ready for upload to Arduino Nano!" -ForegroundColor Green
