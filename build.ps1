# Arduino Build Script for 1Dryer.ino
# This script attempts to compile the Arduino sketch using AVR-GCC

$ErrorActionPreference = "Stop"

# Define paths
$ARDUINO_CORE_PATH = "C:\Users\<USER>\AppData\Local\Arduino15\packages\arduino\hardware\avr\1.8.6"
$AVR_GCC_PATH = "C:\Users\<USER>\AppData\Local\Arduino15\packages\arduino\tools\avr-gcc\7.3.0-atmel3.6.1-arduino7"
$LIBRARIES_PATH = "C:\Users\<USER>\Documents\Arduino\libraries"

# Check if paths exist
if (-not (Test-Path $ARDUINO_CORE_PATH)) {
    Write-Error "Arduino core path not found: $ARDUINO_CORE_PATH"
    exit 1
}

if (-not (Test-Path $AVR_GCC_PATH)) {
    Write-Error "AVR-GCC path not found: $AVR_GCC_PATH"
    exit 1
}

if (-not (Test-Path $LIBRARIES_PATH)) {
    Write-Error "Arduino libraries path not found: $LIBRARIES_PATH"
    exit 1
}

# Define compiler and flags
$COMPILER = "$AVR_GCC_PATH\bin\avr-g++.exe"
$INCLUDES = @(
    "-I$ARDUINO_CORE_PATH\cores\arduino",
    "-I$ARDUINO_CORE_PATH\variants\mega",
    "-I$ARDUINO_CORE_PATH\libraries\EEPROM\src",
    "-I$LIBRARIES_PATH\PID",
    "-I."
)

$DEFINES = @(
    "-DARDUINO=10819",
    "-DARDUINO_AVR_MEGA2560",
    "-DARDUINO_ARCH_AVR",
    "-DF_CPU=16000000L",
    "-D__AVR_ATmega2560__"
)

$CFLAGS = @(
    "-c",
    "-g",
    "-Os",
    "-w",
    "-std=gnu++11",
    "-fpermissive",
    "-fno-exceptions",
    "-ffunction-sections",
    "-fdata-sections",
    "-fno-threadsafe-statics",
    "-Wno-error=narrowing",
    "-MMD",
    "-mmcu=atmega2560"
)

Write-Host "Checking if compiler exists..."
if (-not (Test-Path $COMPILER)) {
    Write-Error "Compiler not found: $COMPILER"
    exit 1
}

Write-Host "Attempting to compile 1Dryer.ino..."
Write-Host "Compiler: $COMPILER"

# Create build directory
$BUILD_DIR = "build"
if (-not (Test-Path $BUILD_DIR)) {
    New-Item -ItemType Directory -Path $BUILD_DIR | Out-Null
}

# Copy .ino file to .cpp for compilation and add Arduino.h include
$SKETCH_CONTENT = Get-Content "1Dryer.ino" -Raw
$SKETCH_WITH_ARDUINO = "#include <Arduino.h>`n" + $SKETCH_CONTENT
$SKETCH_WITH_ARDUINO | Out-File -FilePath "$BUILD_DIR\1Dryer.cpp" -Encoding UTF8

# Compile
$ALL_ARGS = $CFLAGS + $DEFINES + $INCLUDES + @("$BUILD_DIR\1Dryer.cpp", "-o", "$BUILD_DIR\1Dryer.o")

Write-Host "Running compilation command..."
Write-Host "Compiler: $COMPILER"
Write-Host "Arguments: $($ALL_ARGS -join ' ')"

try {
    $process = Start-Process -FilePath $COMPILER -ArgumentList $ALL_ARGS -Wait -PassThru -NoNewWindow -RedirectStandardOutput "build\stdout.txt" -RedirectStandardError "build\stderr.txt"

    if ($process.ExitCode -eq 0) {
        Write-Host "✅ Compilation successful!" -ForegroundColor Green
        Write-Host "Object file created: $BUILD_DIR\1Dryer.o"
    } else {
        Write-Host "❌ Compilation failed with exit code: $($process.ExitCode)" -ForegroundColor Red
        if (Test-Path "build\stderr.txt") {
            Write-Host "Error output:" -ForegroundColor Red
            Get-Content "build\stderr.txt" | Write-Host -ForegroundColor Red
        }
    }
} catch {
    Write-Host "❌ Compilation error: $($_.Exception.Message)" -ForegroundColor Red
}
